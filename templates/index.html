{% extends "base.html" %}

{% block content %}
<div class="row justify-content-center">
    <div class="col-md-8">
        <div class="card shadow">
            <div class="card-header bg-primary text-white">
                <h3 class="card-title mb-0">
                    🎵 Text to Speech Converter
                </h3>
            </div>
            <div class="card-body">
                <form action="{{ url_for('upload_file') }}" method="post" enctype="multipart/form-data" id="uploadForm">
                    <div class="upload-area" id="uploadArea">
                        <div class="file-icon">
                            📄
                        </div>
                        <h5>Drag and drop your text file here</h5>
                        <p class="text-muted">or click to browse</p>
                        <input type="file" name="file" id="fileInput" accept=".txt" style="display: none;" required>
                        <button type="button" class="btn btn-outline-primary" onclick="document.getElementById('fileInput').click()">
                            Choose File
                        </button>
                    </div>
                    
                    <div id="fileInfo" class="mt-3" style="display: none;">
                        <div class="alert alert-info">
                            <strong>Selected file:</strong> <span id="fileName"></span>
                        </div>
                    </div>
                    
                    <div class="mt-3">
                        <button type="submit" class="btn btn-success btn-lg w-100" id="submitBtn" disabled>
                            🎵 Convert to Speech
                        </button>
                    </div>
                </form>
                
                <div class="mt-4">
                    <h6>Instructions:</h6>
                    <ul class="text-muted">
                        <li>Upload a text file (.txt format)</li>
                        <li>Text will be converted to speech using Google Cloud TTS</li>
                        <li>Listen to the audio or download the MP3 file</li>
                        <li>Maximum file size: 16MB</li>
                        <li>Free tier: 1 million characters per month</li>
                    </ul>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
    const uploadArea = document.getElementById('uploadArea');
    const fileInput = document.getElementById('fileInput');
    const fileInfo = document.getElementById('fileInfo');
    const fileName = document.getElementById('fileName');
    const submitBtn = document.getElementById('submitBtn');

    // Handle drag and drop
    uploadArea.addEventListener('dragover', (e) => {
        e.preventDefault();
        uploadArea.style.borderColor = '#0d6efd';
        uploadArea.style.backgroundColor = '#f0f8ff';
    });

    uploadArea.addEventListener('dragleave', () => {
        uploadArea.style.borderColor = '#dee2e6';
        uploadArea.style.backgroundColor = 'white';
    });

    uploadArea.addEventListener('drop', (e) => {
        e.preventDefault();
        uploadArea.style.borderColor = '#dee2e6';
        uploadArea.style.backgroundColor = 'white';
        
        const files = e.dataTransfer.files;
        if (files.length > 0) {
            fileInput.files = files;
            handleFileSelect();
        }
    });

    // Handle file input change
    fileInput.addEventListener('change', handleFileSelect);

    function handleFileSelect() {
        const file = fileInput.files[0];
        if (file) {
            if (file.type === 'text/plain' || file.name.endsWith('.txt')) {
                fileName.textContent = file.name;
                fileInfo.style.display = 'block';
                submitBtn.disabled = false;
            } else {
                alert('Please select a valid text file (.txt)');
                fileInput.value = '';
                fileInfo.style.display = 'none';
                submitBtn.disabled = true;
            }
        }
    }

    // Handle form submission
    document.getElementById('uploadForm').addEventListener('submit', function() {
        submitBtn.innerHTML = '🎵 Converting to Speech...';
        submitBtn.disabled = true;
    });
</script>
{% endblock %}
