{% extends "base.html" %}

{% block content %}
<div class="row justify-content-center">
    <div class="col-md-10">
        <div class="card shadow">
            <div class="card-header bg-success text-white">
                <h3 class="card-title mb-0">
                    🎵 Text to Speech Conversion Complete!
                </h3>
            </div>
            <div class="card-body">
                <div class="alert alert-success">
                    <strong>Success!</strong> Your text file "{{ original_filename }}" has been converted to speech.
                </div>

                {% if audio_file %}
                <!-- Audio Player -->
                <div class="mb-4">
                    <h5>🎧 Listen to Audio</h5>
                    <audio controls class="w-100" style="margin: 10px 0;">
                        <source src="{{ url_for('serve_audio', filename=audio_file) }}" type="audio/mpeg">
                        Your browser does not support the audio element.
                    </audio>
                </div>
                {% endif %}

                <!-- Action Buttons -->
                <div class="mb-4">
                    {% if audio_file %}
                    <a href="{{ url_for('download_audio', filename=audio_file) }}" class="btn btn-success">
                        💾 Download Audio
                    </a>
                    {% endif %}
                    <a href="{{ url_for('index') }}" class="btn btn-primary">
                        📤 Upload Another File
                    </a>
                    <button class="btn btn-outline-secondary" onclick="copyToClipboard()">
                        📋 Copy Text
                    </button>
                </div>
                
                <!-- Text Preview -->
                <div class="mb-4">
                    <h5>📄 Original Text</h5>
                    <div class="text-preview" id="textContent">{{ text_content }}</div>
                </div>

                {% if not tts_available %}
                <div class="alert alert-warning">
                    <strong>Note:</strong> Google Cloud Text-to-Speech is not configured.
                    To enable TTS functionality, please set up Google Cloud credentials.
                </div>
                {% endif %}
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
    function copyToClipboard() {
        const textContent = document.getElementById('textContent').textContent;
        navigator.clipboard.writeText(textContent).then(function() {
            // Show success message
            const btn = event.target;
            const originalText = btn.innerHTML;
            btn.innerHTML = '✅ Copied!';
            btn.classList.remove('btn-outline-secondary');
            btn.classList.add('btn-success');
            
            setTimeout(() => {
                btn.innerHTML = originalText;
                btn.classList.remove('btn-success');
                btn.classList.add('btn-outline-secondary');
            }, 2000);
        }).catch(function(err) {
            alert('Failed to copy text to clipboard');
        });
    }
</script>
{% endblock %}
