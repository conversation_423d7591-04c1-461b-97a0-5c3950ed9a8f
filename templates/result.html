{% extends "base.html" %}

{% block content %}
<div class="row justify-content-center">
    <div class="col-md-10">
        <div class="card shadow">
            <div class="card-header bg-success text-white">
                <h3 class="card-title mb-0">
                    🎵 Text to Speech Conversion Complete!
                </h3>
            </div>
            <div class="card-body">
                <div class="alert alert-success">
                    <strong>Success!</strong> Your text file "{{ original_filename }}" has been converted to speech.
                </div>

                {% if audio_file %}
                <!-- Audio Player -->
                <div class="mb-4">
                    <h5>🎧 Listen to Audio</h5>
                    <audio controls class="w-100" style="margin: 10px 0;" id="audioPlayer">
                        <source src="{{ url_for('serve_audio', filename=audio_file) }}" type="audio/mpeg">
                        Your browser does not support the audio element.
                    </audio>
                    <div class="audio-controls">
                        <button class="btn btn-sm btn-outline-primary" onclick="toggleHighlighting()">
                            <span id="highlightToggle">🔆 Enable Highlighting</span>
                        </button>
                        <button class="btn btn-sm btn-outline-secondary" onclick="resetHighlighting()">
                            🔄 Reset
                        </button>
                        <div class="highlight-info">
                            💡 Enable highlighting to see words light up as they're spoken!
                        </div>
                    </div>
                </div>
                {% endif %}

                <!-- Action Buttons -->
                <div class="mb-4">
                    {% if audio_file %}
                    <a href="{{ url_for('download_audio', filename=audio_file) }}" class="btn btn-success">
                        💾 Download Audio
                    </a>
                    {% endif %}
                    <a href="{{ url_for('index') }}" class="btn btn-primary">
                        📤 Upload Another File
                    </a>
                    <button class="btn btn-outline-secondary" onclick="copyToClipboard()">
                        📋 Copy Text
                    </button>
                </div>
                
                <!-- Text Preview with Highlighting -->
                <div class="mb-4">
                    <h5>📄 Original Text</h5>
                    <div class="text-preview" id="textContent">
                        <div id="highlightedText">{{ text_content }}</div>
                    </div>
                </div>

                {% if not tts_available %}
                <div class="alert alert-warning">
                    <strong>Note:</strong> Google Cloud Text-to-Speech is not configured.
                    To enable TTS functionality, please set up Google Cloud credentials.
                </div>
                {% endif %}
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
    let highlightingEnabled = false;
    let words = [];
    let currentWordIndex = 0;
    let highlightInterval;
    const originalText = `{{ text_content|safe }}`;

    // Initialize text highlighting
    function initializeHighlighting() {
        const textContainer = document.getElementById('highlightedText');

        // Split text into sentences and words
        const sentences = originalText.split(/[.!?]+/).filter(s => s.trim());
        let wordIndex = 0;
        let htmlContent = '';

        sentences.forEach((sentence, sentenceIndex) => {
            if (sentence.trim()) {
                htmlContent += `<span class="sentence" data-sentence="${sentenceIndex}">`;
                const sentenceWords = sentence.trim().split(/\s+/);

                sentenceWords.forEach((word, wordInSentenceIndex) => {
                    if (word.trim()) {
                        htmlContent += `<span class="word" data-word="${wordIndex}" data-sentence="${sentenceIndex}">${word}</span>`;
                        words.push({
                            element: null,
                            text: word,
                            wordIndex: wordIndex,
                            sentenceIndex: sentenceIndex
                        });
                        wordIndex++;

                        // Add space after word (except last word in sentence)
                        if (wordInSentenceIndex < sentenceWords.length - 1) {
                            htmlContent += ' ';
                        }
                    }
                });
                htmlContent += '</span>';

                // Add punctuation and space
                if (sentenceIndex < sentences.length - 1) {
                    htmlContent += '. ';
                }
            }
        });

        textContainer.innerHTML = htmlContent;

        // Update word elements references
        words.forEach((word, index) => {
            word.element = document.querySelector(`[data-word="${index}"]`);
        });
    }

    function toggleHighlighting() {
        const audio = document.getElementById('audioPlayer');
        const toggleBtn = document.getElementById('highlightToggle');

        if (!highlightingEnabled) {
            highlightingEnabled = true;
            toggleBtn.innerHTML = '🔅 Disable Highlighting';
            startHighlighting();
        } else {
            highlightingEnabled = false;
            toggleBtn.innerHTML = '🔆 Enable Highlighting';
            stopHighlighting();
        }
    }

    function startHighlighting() {
        const audio = document.getElementById('audioPlayer');

        // Estimate words per minute (average speaking rate)
        const wordsPerMinute = 150;
        const millisecondsPerWord = (60 / wordsPerMinute) * 1000;

        // Start highlighting when audio plays
        audio.addEventListener('play', function() {
            if (highlightingEnabled) {
                currentWordIndex = 0;
                highlightInterval = setInterval(highlightNextWord, millisecondsPerWord);
            }
        });

        // Pause highlighting when audio pauses
        audio.addEventListener('pause', function() {
            clearInterval(highlightInterval);
        });

        // Reset when audio ends
        audio.addEventListener('ended', function() {
            clearInterval(highlightInterval);
            resetHighlighting();
        });

        // Sync with audio time updates for better accuracy
        audio.addEventListener('timeupdate', function() {
            if (highlightingEnabled && !audio.paused) {
                const progress = audio.currentTime / audio.duration;
                const targetWordIndex = Math.floor(progress * words.length);

                // Adjust current word index if it's too far off
                if (Math.abs(targetWordIndex - currentWordIndex) > 3) {
                    currentWordIndex = targetWordIndex;
                    highlightWord(currentWordIndex);
                }
            }
        });
    }

    function stopHighlighting() {
        clearInterval(highlightInterval);
        resetHighlighting();
    }

    function highlightNextWord() {
        if (currentWordIndex < words.length) {
            highlightWord(currentWordIndex);
            currentWordIndex++;
        } else {
            clearInterval(highlightInterval);
        }
    }

    function highlightWord(index) {
        // Remove previous highlights
        document.querySelectorAll('.word.current').forEach(el => {
            el.classList.remove('current');
            el.classList.add('spoken');
        });

        document.querySelectorAll('.sentence.current').forEach(el => {
            el.classList.remove('current');
        });

        if (index < words.length && words[index].element) {
            const word = words[index];

            // Highlight current word
            word.element.classList.add('current');
            word.element.classList.remove('spoken');

            // Highlight current sentence
            const sentence = document.querySelector(`[data-sentence="${word.sentenceIndex}"]`);
            if (sentence) {
                sentence.classList.add('current');
            }

            // Scroll to keep current word visible
            word.element.scrollIntoView({
                behavior: 'smooth',
                block: 'center',
                inline: 'nearest'
            });
        }
    }

    function resetHighlighting() {
        currentWordIndex = 0;
        clearInterval(highlightInterval);

        // Remove all highlights
        document.querySelectorAll('.word').forEach(el => {
            el.classList.remove('current', 'spoken');
        });

        document.querySelectorAll('.sentence').forEach(el => {
            el.classList.remove('current');
        });
    }

    function copyToClipboard() {
        navigator.clipboard.writeText(originalText).then(function() {
            // Show success message
            const btn = event.target;
            const originalBtnText = btn.innerHTML;
            btn.innerHTML = '✅ Copied!';
            btn.classList.remove('btn-outline-secondary');
            btn.classList.add('btn-success');

            setTimeout(() => {
                btn.innerHTML = originalBtnText;
                btn.classList.remove('btn-success');
                btn.classList.add('btn-outline-secondary');
            }, 2000);
        }).catch(function(err) {
            alert('Failed to copy text to clipboard');
        });
    }

    // Initialize when page loads
    document.addEventListener('DOMContentLoaded', function() {
        initializeHighlighting();
    });
</script>
{% endblock %}
