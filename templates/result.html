{% extends "base.html" %}

{% block content %}
<div class="row justify-content-center">
    <div class="col-md-10">
        <div class="card shadow">
            <div class="card-header bg-success text-white">
                <h3 class="card-title mb-0">
                    ✅ File Uploaded Successfully!
                </h3>
            </div>
            <div class="card-body">
                <div class="alert alert-success">
                    <strong>Success!</strong> Your text file "{{ original_filename }}" has been uploaded and processed.
                </div>
                
                <!-- Action Buttons -->
                <div class="mb-4">
                    <a href="{{ url_for('index') }}" class="btn btn-primary">
                        📤 Upload Another File
                    </a>
                    <button class="btn btn-outline-secondary" onclick="copyToClipboard()">
                        📋 Copy Text
                    </button>
                </div>
                
                <!-- Text Preview -->
                <div class="mb-4">
                    <h5>📄 File Content</h5>
                    <div class="text-preview" id="textContent">{{ text_content }}</div>
                </div>
                
                <!-- File Information -->
                <div class="row">
                    <div class="col-md-6">
                        <div class="card bg-light">
                            <div class="card-body">
                                <h6 class="card-title">📊 File Statistics</h6>
                                <ul class="list-unstyled mb-0">
                                    <li><strong>Filename:</strong> {{ original_filename }}</li>
                                    <li><strong>Characters:</strong> {{ text_content|length }}</li>
                                    <li><strong>Words:</strong> {{ text_content.split()|length }}</li>
                                    <li><strong>Lines:</strong> {{ text_content.split('\n')|length }}</li>
                                </ul>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="card bg-light">
                            <div class="card-body">
                                <h6 class="card-title">🔮 Coming Soon</h6>
                                <ul class="list-unstyled mb-0">
                                    <li>🎵 Text-to-Speech conversion</li>
                                    <li>🎧 Audio playback</li>
                                    <li>💾 Download audio files</li>
                                    <li>🎛️ Voice selection options</li>
                                </ul>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
    function copyToClipboard() {
        const textContent = document.getElementById('textContent').textContent;
        navigator.clipboard.writeText(textContent).then(function() {
            // Show success message
            const btn = event.target;
            const originalText = btn.innerHTML;
            btn.innerHTML = '✅ Copied!';
            btn.classList.remove('btn-outline-secondary');
            btn.classList.add('btn-success');
            
            setTimeout(() => {
                btn.innerHTML = originalText;
                btn.classList.remove('btn-success');
                btn.classList.add('btn-outline-secondary');
            }, 2000);
        }).catch(function(err) {
            alert('Failed to copy text to clipboard');
        });
    }
</script>
{% endblock %}
