import os
import base64
from flask import Flask, request, render_template, flash, redirect, url_for, send_file
from werkzeug.utils import secure_filename
from google.cloud import texttospeech

app = Flask(__name__)
app.secret_key = 'your-secret-key-here'  # Change this to a secure secret key

# Configuration
UPLOAD_FOLDER = 'uploads'
AUDIO_FOLDER = 'audio'
ALLOWED_EXTENSIONS = {'txt'}

# Create directories if they don't exist
os.makedirs(UPLOAD_FOLDER, exist_ok=True)
os.makedirs(AUDIO_FOLDER, exist_ok=True)

app.config['UPLOAD_FOLDER'] = UPLOAD_FOLDER
app.config['AUDIO_FOLDER'] = AUDIO_FOLDER
app.config['MAX_CONTENT_LENGTH'] = 16 * 1024 * 1024  # 16MB max file size

# Initialize Google Cloud Text-to-Speech client
try:
    client = texttospeech.TextToSpeechClient()
    tts_available = True
except Exception as e:
    print(f"Warning: Google Cloud TTS not available: {e}")
    print("The app will work without TTS functionality")
    tts_available = False

def allowed_file(filename):
    return '.' in filename and \
           filename.rsplit('.', 1)[1].lower() in ALLOWED_EXTENSIONS

@app.route('/')
def index():
    return render_template('index.html')

@app.route('/upload', methods=['POST'])
def upload_file():
    if 'file' not in request.files:
        flash('No file selected')
        return redirect(request.url)
    
    file = request.files['file']
    
    if file.filename == '':
        flash('No file selected')
        return redirect(request.url)
    
    if file and allowed_file(file.filename):
        filename = secure_filename(file.filename)
        filepath = os.path.join(app.config['UPLOAD_FOLDER'], filename)
        file.save(filepath)
        
        # Read the text content
        with open(filepath, 'r', encoding='utf-8') as f:
            text_content = f.read()

        # Convert text to speech if TTS is available
        audio_filename = None
        if tts_available and text_content.strip():
            try:
                audio_filename = convert_text_to_speech(text_content, filename)
            except Exception as e:
                flash(f'Text uploaded successfully, but TTS conversion failed: {str(e)}')

        return render_template('result.html',
                             text_content=text_content,
                             original_filename=filename,
                             audio_file=audio_filename,
                             tts_available=tts_available)
    else:
        flash('Invalid file type. Please upload a .txt file.')
        return redirect(url_for('index'))

def convert_text_to_speech(text, original_filename):
    """Convert text to speech using Google Cloud Text-to-Speech"""
    try:
        # Set the text input to be synthesized
        synthesis_input = texttospeech.SynthesisInput(text=text)

        # Build the voice request
        voice = texttospeech.VoiceSelectionParams(
            language_code="en-US",
            ssml_gender=texttospeech.SsmlVoiceGender.NEUTRAL
        )

        # Select the type of audio file you want returned
        audio_config = texttospeech.AudioConfig(
            audio_encoding=texttospeech.AudioEncoding.MP3
        )

        # Perform the text-to-speech request
        response = client.synthesize_speech(
            input=synthesis_input, voice=voice, audio_config=audio_config
        )

        # Save the audio file
        audio_filename = f"{os.path.splitext(original_filename)[0]}.mp3"
        audio_path = os.path.join(app.config['AUDIO_FOLDER'], audio_filename)

        with open(audio_path, 'wb') as out:
            out.write(response.audio_content)

        return audio_filename

    except Exception as e:
        raise Exception(f"Failed to convert text to speech: {str(e)}")

@app.route('/audio/<filename>')
def serve_audio(filename):
    """Serve audio files"""
    return send_file(os.path.join(app.config['AUDIO_FOLDER'], filename))

@app.route('/download/<filename>')
def download_audio(filename):
    """Download audio files"""
    return send_file(os.path.join(app.config['AUDIO_FOLDER'], filename), as_attachment=True)

if __name__ == '__main__':
    app.run(debug=True)
